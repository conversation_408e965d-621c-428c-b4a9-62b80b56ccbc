import matplotlib.pyplot as plt
import numpy as np

# --- 1. Style Setup (for a publication-quality look) ---
# This part sets up the font and text rendering to look like the original.
# Using a serif font like 'Times New Roman' or 'STIXGeneral' is key.
# If you have LaTeX installed, setting 'text.usetex' to True gives the best results.
plt.rcParams.update({
    "font.size": 28,          # General font size
    "axes.titlesize": 36,     # Title font size
    "axes.labelsize": 32,     # Axis label font size
    "xtick.labelsize": 24,    # X-tick label font size
    "ytick.labelsize": 24,    # Y-tick label font size
    "legend.fontsize": 22,    # Legend font size
    "font.family": "serif",   # Use a serif font
    # "text.usetex": True,    # Uncomment if you have a LaTeX installation
})

# --- 2. Data Generation ---
# Since we don't have the original data, we'll create synthetic data
# that mimics the trends shown in each of the four plots.
N = 15  # Number of data points for each line
x = np.arange(N)

# Data for "ALMOST CONSTANT" plot
data_const = {
    'ADDITIVE-PEGGING': np.full(N, 0.33) + np.random.uniform(-0.001, 0.001, N),
    'LEARNED-DYNKIN': np.concatenate([np.zeros(5), np.full(N - 5, 0.25) + np.random.uniform(-0.01, 0.01, N-5)]),
    'DYNKIN': np.full(N, 0.38) + np.random.uniform(-0.001, 0.001, N),
    'HIGHEST-PREDICTION': np.zeros(N),
}

# Data for "UNIFORM" plot
data_uniform = {
    'ADDITIVE-PEGGING': np.linspace(0.95, 0.42, N),
    'LEARNED-DYNKIN': np.linspace(1.0, 0.4, N),
    'DYNKIN': np.full(N, 0.38),
    'HIGHEST-PREDICTION': np.linspace(1.0, 0.4, N),
}

# Data for "ADVERSARIAL" plot
data_adversarial = {
    'ADDITIVE-PEGGING': np.linspace(0.95, 0.33, N),
    'LEARNED-DYNKIN': np.concatenate([np.ones(N-3), [1, 0.55, 0.35]]),
    'DYNKIN': np.full(N, 0.38) + np.random.uniform(-0.01, 0.01, N),
    'HIGHEST-PREDICTION': np.concatenate([np.ones(N-5), np.linspace(1, 0, 5)]),
}

# Data for "UNFAIR" plot
data_unfair = {
    'ADDITIVE-PEGGING': np.full(N, 0.33) + np.random.uniform(-0.01, 0.01, N),
    'LEARNED-DYNKIN': np.zeros(N),
    'DYNKIN': np.full(N, 0.38) + np.random.uniform(-0.01, 0.01, N),
    'HIGHEST-PREDICTION': np.zeros(N),
}

# Combine all data and titles for easy iteration
all_data = [data_const, data_uniform, data_adversarial, data_unfair]
titles = ['ALMOST CONSTANT', 'UNIFORM', 'ADVERSARIAL', 'UNFAIR']

# --- 3. Plotting ---

# Create a figure and a set of subplots
# 1 row, 4 columns. figsize is in (width, height) in inches.
# sharey=True is crucial: it makes all subplots share the same y-axis,
# and automatically hides the y-tick labels for the inner plots.
fig, axs = plt.subplots(1, 4, figsize=(24, 6), sharey=True)

# Define plotting styles for each line
# This dictionary makes it easy to manage markers, styles, and colors.
styles = {
    'ADDITIVE-PEGGING':   {'marker': '^', 'linestyle': '-', 'color': 'tab:blue', 'ms': 12},
    'LEARNED-DYNKIN':     {'marker': 'v', 'linestyle': '-.', 'color': 'tab:orange', 'ms': 12},
    'DYNKIN':             {'marker': 'o', 'linestyle': ':', 'color': 'tab:green', 'ms': 12},
    'HIGHEST-PREDICTION': {'marker': '*', 'linestyle': '--', 'color': 'tab:red', 'ms': 14},
}

# Loop through each subplot (axis) and its corresponding data/title
for i, ax in enumerate(axs):
    data = all_data[i]
    # Plot each algorithm's data on the current axis `ax`
    for name, values in data.items():
        style = styles[name]
        ax.plot(x, values,
                label=name,
                marker=style['marker'],
                linestyle=style['linestyle'],
                color=style['color'],
                markersize=style['ms'],
                linewidth=2)

    # --- 4. Customizing Axes and Titles ---
    ax.set_title(titles[i])
    
    # Set the y-axis limits and ticks for all plots
    ax.set_ylim(0, 1.05)
    ax.set_yticks(np.arange(0, 1.1, 0.2))

    # The original plot has no x-axis ticks or labels, so we remove them
    ax.set_xticks([])
    
    # Add grid lines for better readability (optional)
    ax.grid(alpha=0.3)

# --- 5. Final Touches (Labels, Legend, Layout) ---

# Set the Y-axis label only for the first subplot
axs[0].set_ylabel('Fairness')

# Add the legend only to the first subplot
axs[0].legend()

# Adjust the spacing between subplots to be minimal
plt.subplots_adjust(wspace=0.05)

# Display the plot
plt.savefig("test.png")