import numpy as np
import random
def internal_threshold(v_hat, v, tau=0.388701, theta=0.6275, p0=0.541625, p1=0.083457, epsilon=0.01):
    """
    Implementation of the secretary problem with predictions.

    Args:
        v_hat (dict): Predicted values for candidates.
        v (dict): Actual values for candidates.
        tau (float): Time threshold.
        theta (float): Prediction error threshold.
        p0 (float): Probability for t_ihat = tau.
        p1 (float): Probability for t_ihat = tau + epsilon.
        epsilon (float): Epsilon value for time.
    """
    n_cal = list(v_hat)
    
    # Sort candidates by prediction
    sorted_by_prediction = sorted(n_cal, key=lambda i: v_hat[i], reverse=True)
    i_hat = sorted_by_prediction[0]
    j_hat = sorted_by_prediction[1]
    k_hat = sorted_by_prediction[2]

    # Generate arrival times internally
    t = {i: random.random() for i in n_cal}
    '''
    rand_val = random.random()
    if rand_val < p0:
        t[i_hat] = tau
    elif rand_val < p0 + p1:
        t[i_hat] = tau + epsilon
    else:
        t[i_hat] = 1.0
    '''

    # Sort candidates by arrival time
    sorted_by_arrival = sorted(n_cal, key=lambda i: t[i])

    X = 0
    mode = "Prediction"
    best_so_far_val = -1

    for i in sorted_by_arrival:

        # Update best so far
        if v[i] > best_so_far_val:
            best_so_far_val = v[i]
            is_best_so_far = True
        else:
            is_best_so_far = False

        # Check for prediction error
        if abs(1 - (v_hat[i] / v[i])) > theta:
            if X == 0 and (v_hat[i] / v[i]) - 1 > theta and i == i_hat and mode == "Prediction":
                if t[i] > tau and is_best_so_far:
                    return i, t
                X = (1 - theta) * v_hat[j_hat]
            elif X > 0 and i == j_hat:
                X = (1 - theta) * v_hat[k_hat]
            else:
                X = 0
            mode = "Secretary"

        # Hiring logic
        if mode == "Prediction" and i == i_hat:
            return i, t
        
        if mode == "Secretary" and t[i] > tau and is_best_so_far and v[i] >= X:
            return i, t

    # If no one is hired, hire the last one
    return sorted_by_arrival[-1], t

if __name__ == '__main__':
    # Example Usage
    num_candidates = 10
    candidates = list(range(num_candidates))
    
    # Ground truth values (unknown to the algorithm)
    v_actual = {i: np.random.rand() for i in candidates}
    
    # Predicted values (with some noise)
    v_predictions = {i: v_actual[i] + np.random.normal(0, 0.001) for i in candidates}

    # The secretary_problem function now generates its own arrival times
    hired, arrival_times = secretary_problem(v_predictions, v_actual)
    
    print(f"Candidates (ID: Actual Value, Predicted Value, Arrival Time):")
    for i in sorted(candidates, key=lambda c: arrival_times[c]):
        print(f"  - {i}: v={v_actual[i]:.2f}, v_hat={v_predictions[i]:.2f}, t={arrival_times[i]:.2f}")

    print(f"\nHired candidate: {hired}")
    print(f"Hired candidate's actual value: {v_actual[hired]:.2f}")

    best_candidate = max(v_actual, key=v_actual.get)
    print(f"\nBest possible candidate: {best_candidate}")
    print(f"Best possible value: {v_actual[best_candidate]:.2f}")