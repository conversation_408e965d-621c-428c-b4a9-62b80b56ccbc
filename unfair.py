import numpy as np
import os

def generate_unfair_instance(n, epsilon):
    """
    Generates a single problem instance for the 'Unfair' type.
    
    In Unfair all candidates have values that are at most a (1 + ε) multiplicative factor apart. 
    Formally, ui is a uniform value in [1 − ε/4, 1 + ε/4], and since (1 + ε/4)/(1 − ε/4) ≤ (1 + ε) 
    we have that the smallest and largest value are indeed very close. We set uˆi = un−r(i)+1 where r(i) is the rank of ui, 
    i.e., predictions create a completely inverted order.

    Args:
        n (int): The number of elements.
        epsilon (float): The error parameter.

    Returns:
        tuple: A tuple containing (actual_values, predicted_values).
    """
    # 1. Generate actual values from a uniform distribution
    low = 1 - epsilon / 4.0
    high = 1 + epsilon / 4.0
    actual_values = np.random.uniform(low, high, size=n)
    
    # 2. Create predicted values that are in the exact reverse order of the actual values.
    # argsort gives the indices that would sort the array.
    # So, actual_values[sorted_indices] is the sorted array.
    sorted_indices = np.argsort(actual_values)
    
    # The predicted values are the sorted actual values in reverse order.
    # The i-th element in the sorted list of actual values gets the (n-i)-th element as its prediction.
    predicted_values = np.zeros_like(actual_values)
    predicted_values[sorted_indices] = np.sort(actual_values)[::-1]
    
    return actual_values, predicted_values

def save_instance_to_file(n, actual_values, predicted_values, filepath):
    """
    Saves the generated instance to a .txt file in the specified format.

    Args:
        n (int): The number of elements.
        actual_values (np.array): Array of actual values.
        predicted_values (np.array): Array of predicted values.
        filepath (str): The path to the output file.
    """
    with open(filepath, 'w') as f:
        # Line 1: N
        f.write(f"{n}\n")

        # Line 2: N numbers representing elements' real values
        actual_values_str = " ".join(map(str, actual_values))
        f.write(f"{actual_values_str}\n")

        # Line 3: N numbers representing elements' predicted values
        predicted_values_str = " ".join(map(str, predicted_values))
        f.write(f"{predicted_values_str}\n")

def main():
    """
    Main function to generate all required test cases.
    """
    # --- Parameters ---
    N = 100
    # Epsilon values from 0.0 to 0.9.
    epsilon_values = np.arange(0, 1.0, 0.05)

    # --- Directory Setup ---
    output_dir = "unfair_test_cases"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")

    # --- Generation Loop ---
    NUM_ATTEMPTS_PER_CASE = 10000
    total_files = len(epsilon_values) * NUM_ATTEMPTS_PER_CASE
    print(f"Generating {total_files} test files for N={N} ({NUM_ATTEMPTS_PER_CASE} attempts per case)...")

    for epsilon in epsilon_values:
        for attempt in range(NUM_ATTEMPTS_PER_CASE):
            # Generate the data
            actual_v, predicted_v = generate_unfair_instance(N, epsilon)

            # Create a descriptive filename
            filename = f"unfair_n{N}_eps{epsilon:.3f}_{attempt+1}.txt"
            filepath = os.path.join(output_dir, filename)

            # Save the data to the file
            save_instance_to_file(N, actual_v, predicted_v, filepath)
        
        print(f"Generated {NUM_ATTEMPTS_PER_CASE} files for epsilon={epsilon:.3f}")

    print("\nAll test cases generated.")

if __name__ == "__main__":
    main()
