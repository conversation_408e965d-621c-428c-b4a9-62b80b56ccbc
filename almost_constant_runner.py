import os
import random
import re
import numpy as np
import matplotlib.pyplot as plt
from main import secretary_problem
from learned_dynkin import learned_dynkin_algorithm
from robustness_test import Algorithm
from additive_pegging import additive_pegging_algorithm
from ordered_additive_pegging import ordered_additive_pegging_algorithm
def run_single_test(test_filepath, num_runs=10000):
    """
    Runs a single, specified test case for all algorithms.

    Args:
        test_filepath (str): The path to the test case TXT file.
        num_runs (int): The number of times to run the test to find the expected value.
    """
    # Load the test case data from .txt file
    with open(test_filepath, 'r') as f:
        lines = f.readlines()
    
    N = int(lines[0].strip())
    v_actual_list = [float(v) for v in lines[1].strip().split()]
    v_predictions_list = [float(v) for v in lines[2].strip().split()]

    # Augment to prevent duplicates by adding small random noise
    #v_actual_list = [v + random.uniform(1e-8, 1e-4) for v in v_actual_list]
    #v_predictions_list = [v + random.uniform(1e-8, 1e-4) for v in v_predictions_list]

    v_actual = {i: v_actual_list[i] for i in range(N)}
    v_predictions = {i: v_predictions_list[i] for i in range(N)}

    # Extract description from filename
    description = os.path.basename(test_filepath).replace('.txt', '').replace('_', ' ').title()

    #print(f"\n--- Running Test: {test_filepath} ---")
    #print(f"Description: {description}")
    #print(f"Averaging over {num_runs} runs to find expected value...")

    # Define the algorithms to be tested
    algorithms = [
        Algorithm("Ordered Learned Dynkin", secretary_problem),
        Algorithm("Learned Dynkin", learned_dynkin_algorithm),
        Algorithm("Additive Pegging", additive_pegging_algorithm),
        Algorithm("Ordered Additive Pegging", ordered_additive_pegging_algorithm),
    ]

    # Run the test for each algorithm
    results = {}
    for alg in algorithms:
        hired_values = []
        for _ in range(num_runs):
            # The same v_actual and v_predictions are used for each run
            hired_candidate = alg.run(v_predictions, v_actual)
            if hired_candidate == -1:
                hired_values.append(0)
            else:
                ratio = v_actual[hired_candidate] / max(v_actual.values())
                hired_values.append(ratio)


            #print(hired_candidate)
        results[alg.name] = np.mean(hired_values)

    # Report the results
    #print(f"\n{'Algorithm':<25} {'Expected Hired Value':<25}")
    #print("-" * 50)
    #for name, avg_value in results.items():
    #    print(f"{name:<25} {avg_value:<25.4f}")
    #print("-" * 50)
    return results

if __name__ == '__main__':
    # uniform = 'uniform_test_cases'
    adversarial = 'adversarial_test_cases'
    almost_constant = 'almost_constant_test_cases'
    deception = 'spike_deception_test_cases'
    granular_deception = 'granular_decoy_error_cases'
    three_tier_deception = 'three_tier_deception_cases'
    test_dir = almost_constant
    
    all_files = sorted([os.path.join(test_dir, f) for f in os.listdir(test_dir) if f.endswith('.txt')])

    # Group files by their base name (e.g., 'case_01_decoyeps0.0')
    file_groups = {}
    for f in all_files:
        base_name_match = re.match(r'(.+)_(\d+)\.txt$', os.path.basename(f))
        if base_name_match:
            base_name = base_name_match.group(1)
            if base_name not in file_groups:
                file_groups[base_name] = []
            file_groups[base_name].append(f)

    # Data collection for plotting
    eps_values = []
    final_results_by_alg = {}

    print(f"Found {len(file_groups)} groups of test cases in '{test_dir}'.")

    for base_name, file_list in file_groups.items():
        # Extract eps value from the base name
        match = re.search(r'eps(\d+\.?\d*)', base_name)
        if not match:
            print(f"Warning: Could not find eps value in group {base_name}")
            continue
        try:
            eps = float(match.group(1))
            eps_values.append(eps)
            print(f"\n--- Processing Group: {base_name} (Epsilon: {eps}) ---")
        except:
            eps_values.append(0)
            print(f"Warning: Could not convert eps value to float in group {base_name}")


        # Run tests for all files in the group and collect results
        group_results_by_alg = {}
        for test_file in file_list:
            num_runs_for_test = 1
            results = run_single_test(test_file, num_runs=num_runs_for_test)
            for alg_name, value in results.items():
                if alg_name not in group_results_by_alg:
                    group_results_by_alg[alg_name] = []
                group_results_by_alg[alg_name].append(value)
        
        # For each algorithm, find the minimum result from the group
        mean_results = {}
        for alg_name, values in group_results_by_alg.items():
            mean_results[alg_name] = sum(values) / len(values)
            print(f"  -> Min value for {alg_name}: {mean_results[alg_name]:.4f}")

        # Append the minimum results to the final plot data
        for alg_name, mean_value in mean_results.items():
            if alg_name not in final_results_by_alg:
                final_results_by_alg[alg_name] = []
            final_results_by_alg[alg_name].append(mean_value)

    # Sort results based on eps_values for correct plotting
    sorted_indices = np.argsort(eps_values)
    sorted_eps_values = np.array(eps_values)[sorted_indices]
    for alg_name in final_results_by_alg:
        final_results_by_alg[alg_name] = np.array(final_results_by_alg[alg_name])[sorted_indices]
    
    results_by_alg = final_results_by_alg # Use the aggregated results for plotting

    # --- Plotting ---
    # Special handling for 'almost_constant' to generate a plot for each 'k'
    
    plt.figure(figsize=(10, 6))
    for alg_name, values in results_by_alg.items():
        plt.plot(sorted_eps_values, values, marker='o', linestyle='-', label=alg_name)
        
    plt.xlabel("Epsilon (eps)")
    plt.ylabel("Expected Hired Value (Ratio to Max)")
    plt.title(f"Algorithm Performance vs. Epsilon on {test_dir.replace('_', ' ').title()}")
    plt.legend()
    plt.grid(True)
        
    output_filename = f"{test_dir}_performance_graph.png"
    plt.savefig(output_filename)
    print(f"\nGraph saved to {output_filename}")
    plt.show()
