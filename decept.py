import numpy as np
import os

def generate_three_tier_instance(n, spike_value, decoy_value, background_range, spike_epsilon, decoy_epsilon):
    """
    Generates a deception instance with a three-tier value structure.

    Args:
        n (int): The number of elements.
        spike_value (float): The actual value of the best element.
        decoy_value (float): The actual value of the decoy element.
        background_range (tuple): A (min, max) tuple for other elements' values.
        spike_epsilon (float): The prediction error for the true spike.
        decoy_epsilon (float): The multiplicative prediction error for the decoy.

    Returns:
        tuple: A tuple containing (actual_values, predicted_values).
    """
    # 1. Initialize all elements to the low "background" values.
    actual_values = np.random.uniform(low=background_range[0], high=background_range[1], size=n)
    predicted_values = actual_values * np.random.uniform(0.95, 1.05, size=n)

    # 2. Choose two distinct indices for the spike and the decoy.
    indices = np.arange(n)
    true_best_idx, decoy_idx = np.random.choice(indices, size=2, replace=False)
    
    # 3. Set the actual values for the spike and the decoy.
    actual_values[true_best_idx] = spike_value
    actual_values[decoy_idx] = decoy_value
    
    # 4. Set the prediction for the spike (underestimated by spike_epsilon).
    spike_prediction = spike_value * (1.0 - spike_epsilon)
    predicted_values[true_best_idx] = spike_prediction
    
    # 5. Set the prediction for the decoy (overestimated by decoy_epsilon).
    decoy_prediction = decoy_value * (1.0 + decoy_epsilon)
    predicted_values[decoy_idx] = decoy_prediction
    
    return actual_values, predicted_values

def save_instance_to_file(n, actual_values, predicted_values, filepath):
    """Saves the generated instance to a .txt file."""
    def format_num(x):
        return str(int(x)) if x == int(x) else f"{x:.4f}"

    with open(filepath, 'w') as f:
        f.write(f"{n}\n")
        f.write(" ".join(map(format_num, actual_values)) + "\n")
        f.write(" ".join(map(format_num, predicted_values)) + "\n")

def main():
    """Main function to generate a structured set of test cases."""
    # --- Three-Tier Value Structure ---
    N = 100
    SPIKE_VALUE = 1000.0
    DECOY_VALUE = 100.0
    BACKGROUND_RANGE = (1, 5)
    
    # --- Error Parameters ---
    SPIKE_EPSILON = 0.1 # A fixed, small error for the best item's prediction.
    
    # --- Parameter to Vary: Decoy's Multiplicative Error ---
    decoy_epsilon_values = [
        0.0,    # Case 1: No decoy error (P_decoy = 100) -> Deception Fails
        0.5,
        1.0, 
        2.0, 
        5.0,    # Case 2: Medium error (P_decoy = 600) -> Deception Fails
        7.5,    # Case 3: Error just below threshold (P_decoy = 850) -> Deception Fails
        8.5,    # Case 4: Error just above threshold (P_decoy = 950) -> Deception Succeeds
        10.0,   # Case 5: Clear deception (P_decoy = 1100) -> Deception Succeeds
        20.0,   # Case 6: Overwhelming deception (P_decoy = 2100) -> Deception Succeeds
        50.0,     # Case 7: Extremely large error (P_decoy = 5100) -> Deception Succeeds
        100.0,    # Case 8: Extremely large error (P_decoy = 10100) -> Deception Succeeds
    ]

    # --- Directory Setup ---
    output_dir = "three_tier_deception_cases"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")

    # --- Explanatory Printout ---
    spike_pred_val = SPIKE_VALUE * (1.0 - SPIKE_EPSILON)
    print("--- Three-Tier Test Case Generation ---")
    print(f"Spike (V_spike): {SPIKE_VALUE}, Decoy (V_decoy): {DECOY_VALUE}")
    print(f"Spike's Predicted Value (P_spike): {spike_pred_val:.2f}")
    print(f"Deception succeeds if P_decoy > {spike_pred_val:.2f}")
    print("Threshold for decoy error (eps_d) is 8.0")
    print("-" * 30)

    # --- Generation Loop ---
    NUM_ATTEMPTS_PER_EPS = 10000
    print(f"Generating {len(decoy_epsilon_values) * NUM_ATTEMPTS_PER_EPS} test files ({NUM_ATTEMPTS_PER_EPS} attempts per epsilon value)...")

    for i, decoy_eps in enumerate(decoy_epsilon_values):
        for attempt in range(NUM_ATTEMPTS_PER_EPS):
            actual_v, predicted_v = generate_three_tier_instance(
                N, SPIKE_VALUE, DECOY_VALUE, BACKGROUND_RANGE, SPIKE_EPSILON, decoy_eps
            )

            filename = f"case_{i+1:02d}_decoyeps{decoy_eps:.1f}_{attempt+1}.txt"
            filepath = os.path.join(output_dir, filename)

            save_instance_to_file(N, actual_v, predicted_v, filepath)
        
        # Also print the key values for this generated case
        decoy_pred = DECOY_VALUE * (1 + decoy_eps)
        status = "SUCCEEDS" if decoy_pred > spike_pred_val else "FAILS"
        #print(f"Generated {NUM_ATTEMPTS_PER_EPS} files for decoy_eps={decoy_eps:.1f} (P_decoy={decoy_pred:.2f}, Deception {status})")

    print("\nAll test cases generated.")

if __name__ == "__main__":
    main()
