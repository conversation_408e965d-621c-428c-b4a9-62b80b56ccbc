import numpy as np
import os

def generate_adversarial_instance(n, epsilon):
    """
    Generates a single problem instance for the 'Adversarial' type.

    Args:
        n (int): The number of elements.
        epsilon (float): The error parameter for prediction perturbation.

    Returns:
        tuple: A tuple containing (actual_values, predicted_values).
    """
    # 1. Generate actual values: v(i) ~ Exponential(1)
    actual_values = np.random.exponential(scale=1.0, size=n)
    
    # Initialize an array for predicted values, preserving original order.
    predicted_values = np.zeros_like(actual_values)

    # 2. Identify indices of top 50 and bottom 50 candidates based on actual values.
    # np.argsort() returns the indices that would sort the array in ascending order.
    sorted_indices = np.argsort(actual_values)
    
    # The first n/2 indices correspond to the smallest values.
    # The last n/2 indices correspond to the largest values.
    num_half = n // 2
    bottom_indices = sorted_indices[:num_half]
    top_indices = sorted_indices[num_half:]
    
    # 3. Adversarially perturb the values.
    # For top 50 candidates, set v_hat(i) = (1 - epsilon) * v(i)
    # This underestimates the best items.
    predicted_values[top_indices] = (1 - epsilon) * actual_values[top_indices]
    
    # For other 50 candidates, set v_hat(i) = (1 + epsilon) * v(i)
    # This overestimates the worst items.
    predicted_values[bottom_indices] = (1 + epsilon) * actual_values[bottom_indices]
    
    return actual_values, predicted_values

def save_instance_to_file(n, actual_values, predicted_values, filepath):
    """
    Saves the generated instance to a .txt file in the specified format.

    Args:
        n (int): The number of elements.
        actual_values (np.array): Array of actual values.
        predicted_values (np.array): Array of predicted values.
        filepath (str): The path to the output file.
    """
    with open(filepath, 'w') as f:
        # Line 1: N
        f.write(f"{n}\n")

        # Line 2: N numbers representing elements' real values
        actual_values_str = " ".join(map(str, actual_values))
        f.write(f"{actual_values_str}\n")

        # Line 3: N numbers representing elements' predicted values
        predicted_values_str = " ".join(map(str, predicted_values))
        f.write(f"{predicted_values_str}\n")
def main():
    """
    Main function to generate all required test cases.
    """
    # --- Parameters ---
    N = 100
    k_values = [1]
    # Epsilon values from 0.0 to 0.9. The case e=1 is omitted.
    epsilon_values = np.arange(0, 1.0, 0.05)

    # --- Directory Setup ---
    output_dir = "adversarial_test_cases"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")

    # --- Generation Loop ---
    NUM_ATTEMPTS_PER_CASE = 10000
    total_files = len(k_values) * len(epsilon_values) * NUM_ATTEMPTS_PER_CASE
    print(f"Generating {total_files} test files for N={N} ({NUM_ATTEMPTS_PER_CASE} attempts per case)...")

    for k in k_values:
        for epsilon in epsilon_values:
            for attempt in range(NUM_ATTEMPTS_PER_CASE):
                # Generate the data
                actual_v, predicted_v = generate_adversarial_instance(N, epsilon)

                # Create a descriptive filename that includes k and the attempt number
                filename = f"adversarial_n{N}_k{k}_eps{epsilon:.3f}_{attempt+1}.txt"
                filepath = os.path.join(output_dir, filename)

                # Save the data to the file
                save_instance_to_file(N, actual_v, predicted_v, filepath)
            
            print(f"Generated {NUM_ATTEMPTS_PER_CASE} files for k={k}, epsilon={epsilon:.3f}")

    print("\nAll test cases generated.")

if __name__ == "__main__":
    main()
