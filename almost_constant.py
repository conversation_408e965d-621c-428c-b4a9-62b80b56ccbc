import numpy as np
import os

def generate_almost_constant_instance(n, epsilon):
    """
    Generates a single problem instance for the 'Almost-Constant' type.

    Args:
        n (int): The number of elements.
        k (int): The number of "special" candidates with high actual values.
        epsilon (float): The error parameter.

    Returns:
        tuple: A tuple containing (actual_values, predicted_values).
    """
    # 1. Start with the base values before perturbation.
    # All predicted values are set to 1.
    base_predicted_values = np.ones(n)
    
    # All actual values are initially set to 1.
    base_actual_values = np.ones(n)
    
    # 2. Select k candidates uniformly at random (without replacement).
    all_indices = np.arange(n)
    special_indices = np.random.choice(all_indices, size=1, replace=False)
    
    # 3. Set the actual values for these k special candidates.
    # The special value is 1 / (1 - epsilon).
    special_value = 1.0 / (1.0 - epsilon)
    base_actual_values[special_indices] = special_value
    
    # 4. Perturb ALL values with a random value from U[0, 0.01] for tie-breaking.
    # This perturbation is applied via addition.
    #tie_break_perturbation_actual = np.random.uniform(low=0.0, high=0.0001, size=n)
    #tie_break_perturbation_predicted = np.random.uniform(low=0.0, high=0.0001, size=n)

    final_actual_values = base_actual_values
    final_predicted_values = base_predicted_values 
    
    return final_actual_values, final_predicted_values

def save_instance_to_file(n, actual_values, predicted_values, filepath):
    """
    Saves the generated instance to a .txt file in the specified format.

    Args:
        n (int): The number of elements.
        actual_values (np.array): Array of actual values.
        predicted_values (np.array): Array of predicted values.
        filepath (str): The path to the output file.
    """
    with open(filepath, 'w') as f:
        # Line 1: N
        f.write(f"{n}\n")

        # Line 2: N numbers representing elements' real values
        actual_values_str = " ".join(map(str, actual_values))
        f.write(f"{actual_values_str}\n")

        # Line 3: N numbers representing elements' predicted values
        predicted_values_str = " ".join(map(str, predicted_values))
        f.write(f"{predicted_values_str}\n")

def main():
    """
    Main function to generate all required test cases.
    """
    # --- Parameters ---
    N = 100
    k_values = [1]
    # Epsilon values from 0.0 to 0.9. The case e=1 is omitted.
    epsilon_values = np.arange(0, 1.0, 0.05)

    # --- Directory Setup ---
    output_dir = "almost_constant_test_cases"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")

    # --- Generation Loop ---
    NUM_ATTEMPTS_PER_CASE = 10000
    total_files = len(k_values) * len(epsilon_values) * NUM_ATTEMPTS_PER_CASE
    print(f"Generating {total_files} test files for N={N} ({NUM_ATTEMPTS_PER_CASE} attempts per case)...")

    for k in k_values:
        for epsilon in epsilon_values:
            for attempt in range(NUM_ATTEMPTS_PER_CASE):
                # Generate the data
                actual_v, predicted_v = generate_almost_constant_instance(N, epsilon)

                # Create a descriptive filename that includes k and the attempt number
                filename = f"almostconstant_n{N}_k{k}_eps{epsilon:.3f}_{attempt+1}.txt"
                filepath = os.path.join(output_dir, filename)

                # Save the data to the file
                save_instance_to_file(N, actual_v, predicted_v, filepath)
            
            print(f"Generated {NUM_ATTEMPTS_PER_CASE} files for k={k}, epsilon={epsilon:.3f}")

    print("\nAll test cases generated.")

if __name__ == "__main__":
    main()
