import numpy as np

def learned_dynkin_algorithm(v_hat, v, tau=0.313, theta=0.646):
    """
    Implementation of the Learned Dynkin algorithm.

    Args:
        v_hat (dict): Predicted values for candidates.
        v (dict): Actual values for candidates.
        tau (float): Time threshold for Secretary mode.
        theta (float): Prediction error threshold.
    """
    n_cal = list(v_hat.keys())
    for k, val in v_hat.items():
        v_hat[k] = val + np.random.normal(0, 0.001)
    for k, val in v.items():
        v[k] = val + np.random.normal(0, 0.001)

    
    # Determine i_hat
    i_hat = max(n_cal, key=lambda i: v_hat[i])
    
    # Process candidates in random order (simulated by random arrival times)
    arrival_times = {i: np.random.rand() for i in n_cal}
    sorted_by_arrival = sorted(n_cal, key=lambda i: arrival_times[i])

    mode = "Prediction"
    best_so_far_val = -1
    
    for idx, i in enumerate(sorted_by_arrival):
        # The problem description implies t_i is the normalized arrival order
        t_i = arrival_times[i]

        # Update best so far
        if v[i] > best_so_far_val:
            best_so_far_val = v[i]
            is_best_so_far = True
        else:
            is_best_so_far = False

        # Check for prediction error and switch mode if necessary
        if abs(1 - (v_hat[i] / v[i])) > theta:
            mode = "Secretary"

        # Hiring logic
        if mode == "Prediction" and i == i_hat:
            return i, arrival_times
        
        if mode == "Secretary" and t_i > tau and is_best_so_far:
            return i, arrival_times

    # If no one is hired, hire the last one
    return -1, arrival_times

if __name__ == '__main__':
    # Example Usage
    num_candidates = 10
    candidates = list(range(num_candidates))
    
    # Ground truth values
    v_actual = {i: np.random.rand() for i in candidates}
    
    # Predicted values
    v_predictions = {i: v_actual[i] + np.random.normal(0, 0.1) for i in candidates}
    
    # The learned_dynkin_algorithm function will use its default parameters
    hired, times = learned_dynkin_algorithm(v_predictions, v_actual)
    
    print("Algorithm: Learned Dynkin")
    print(f"Candidates (ID: Actual Value, Predicted Value, Arrival Time):")
    for i in sorted(candidates, key=lambda c: times[c]):
        print(f"  - {i}: v={v_actual[i]:.2f}, v_hat={v_predictions[i]:.2f}, t={times[i]:.2f}")

    print(f"\nHired candidate: {hired}")
    print(f"Hired candidate's actual value: {v_actual[hired]:.2f}")

    best_candidate = max(v_actual, key=v_actual.get)
    print(f"\nBest possible candidate: {best_candidate}")
    print(f"Best possible value: {v_actual[best_candidate]:.2f}")
