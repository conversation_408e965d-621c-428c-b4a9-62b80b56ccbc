import numpy as np
import random

def ordered_additive_pegging_algorithm(v_predictions, v_actual, tau=0.5, epsilon=0.001):
    """
    Implementation of the Ordered Additive Pegging algorithm.
    This version introduces a specific arrival time distribution for the highest-predicted candidate.
    """
    N = len(v_predictions)
    if N == 0:
        return None, {}

    pegged = set()
    ti = dict()
    for i in range(N):
        ti[i] = np.random.rand()
    
    
    max_so_far = -np.inf
    
    ihat = 0
    mx = -np.inf
    for i in v_predictions.keys():
        if v_predictions[i] > mx:
            mx = v_predictions[i]
            ihat = i
    if random.random() < 0.5:
        ti[ihat] = 1/2 - tau
    else:
        ti[ihat] = 1.0
    order = [i for i in range(N)]   
    order = sorted(order, key=lambda i: ti[i])
    maxdiff = 0
    for i in order:
        if i in pegged:
            if len(pegged) == 1:
                return i, {}
            else:
                pegged.remove(i)
        F = (v_actual[i] > max_so_far) and (ti[i] > 1/2)
        max_so_far = max(max_so_far, v_actual[i])
        C = (i == ihat)
        maxdiff = max(maxdiff, abs(v_predictions[i] - v_actual[i]))
        if C and F:
            return i , {}
        elif C and not F:
            laters = []
            for x in range(N):
                if ti[x] > ti[i]:
                    laters.append(x)
            newset = set()
            for x in laters:
                if v_actual[i] < v_predictions[x] + maxdiff:
                    newset.add(x)
            pegged = newset
            if len(pegged) == 0:
                return i, {}
        elif not C and F:
            if v_actual[i] > v_predictions[ihat] - maxdiff:
                return i, {}
    return -1, {}



